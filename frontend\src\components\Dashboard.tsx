import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import ETRIBChatbot from './ETRIBChatbot';

const Dashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const { isDark, toggleTheme } = useTheme();
  const [activeSection, setActiveSection] = useState('dashboard');

  const menuItems = [
    { id: 'dashboard', name: 'Dashboard', icon: '📊' },
    { id: 'signals', name: 'Signals', icon: '📈', description: 'Premium trading signals' },
    { id: 'course', name: 'Course', icon: '📚', description: 'Full GOAT strategy' },
    { id: 'one-on-one', name: 'One on One', icon: '👥', description: 'Personal coaching' },
    { id: 'trade-with-kojo', name: 'Trade With Kojo', icon: '🎯', description: 'Live trading sessions' },
    { id: 'collaborations', name: 'Collaborations', icon: '🤝', description: 'Brand partnerships' },
    { id: 'academy', name: 'Academy', icon: '🎓', description: 'Beginner training' },
    { id: 'booking', name: 'Booking', icon: '📅', description: 'Schedule sessions' },
    { id: 'enquiry', name: 'Enquiry', icon: '❓', description: 'Get in touch' }
  ];

  const renderContent = () => {
    switch (activeSection) {
      case 'signals':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-dark-900 dark:text-white">Premium Trading Signals</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map((signal) => (
                <div key={signal} className="card p-6">
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-lg font-semibold text-gray-900 dark:text-white">EUR/USD</span>
                    <span className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 px-2 py-1 rounded-full text-sm">BUY</span>
                  </div>
                  <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex justify-between">
                      <span>Entry:</span>
                      <span className="font-medium">1.0850</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Stop Loss:</span>
                      <span className="font-medium">1.0820</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Take Profit:</span>
                      <span className="font-medium">1.0920</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );
      case 'course':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Full GOAT Strategy Course</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Course Progress</h3>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-600 dark:text-gray-400">Module 1: Basics</span>
                      <span className="text-primary-600">100%</span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div className="bg-primary-600 h-2 rounded-full w-full"></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-600 dark:text-gray-400">Module 2: Technical Analysis</span>
                      <span className="text-primary-600">75%</span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div className="bg-primary-600 h-2 rounded-full w-3/4"></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-600 dark:text-gray-400">Module 3: Risk Management</span>
                      <span className="text-gray-500">0%</span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div className="bg-gray-300 h-2 rounded-full w-0"></div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Next Lesson</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">Advanced Chart Patterns</p>
                <button className="btn-primary w-full">Continue Learning</button>
              </div>
            </div>
          </div>
        );
      default:
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                Welcome back, {user?.name}! 👋
              </h2>
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              Here's your complete navigation hub. Click any card to access that section.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {menuItems.slice(1).map((item) => (
                <div
                  key={item.id}
                  onClick={() => setActiveSection(item.id)}
                  className="card p-6 cursor-pointer hover:shadow-lg transition-shadow duration-200"
                >
                  <div className="text-3xl mb-4">{item.icon}</div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {item.name}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {item.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex">
      {/* Sidebar */}
      <div className="w-64 bg-white dark:bg-gray-800 shadow-lg">
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-8">
            <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
              </svg>
            </div>
            <div>
              <h1 className="text-lg font-bold text-gray-900 dark:text-white">KOJOFOREX</h1>
            </div>
          </div>

          <nav className="space-y-2">
            {menuItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveSection(item.id)}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                  activeSection === item.id
                    ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <span className="text-xl">{item.icon}</span>
                <span className="font-medium">{item.name}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="absolute bottom-0 left-0 right-0 w-64 p-6 border-t border-gray-200 dark:border-gray-700">
          <div className="bg-primary-50 dark:bg-primary-900 rounded-lg p-4">
            <h4 className="font-semibold text-primary-900 dark:text-primary-100 mb-2">Need Help?</h4>
            <p className="text-sm text-primary-700 dark:text-primary-300 mb-3">
              Contact our support team for assistance.
            </p>
            <button className="text-sm bg-primary-600 text-white px-3 py-1 rounded">
              Contact Support
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Bar */}
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 px-6 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-semibold text-gray-900 dark:text-white capitalize">
              {activeSection.replace('-', ' ')}
            </h1>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={toggleTheme}
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                {isDark ? '☀️' : '🌙'}
              </button>
              
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {user?.name?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className="text-sm">
                  <div className="font-medium text-gray-900 dark:text-white">{user?.name}</div>
                  <div className="text-gray-500 dark:text-gray-400">{user?.email}</div>
                </div>
                <button
                  onClick={logout}
                  className="text-sm text-gray-500 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400"
                >
                  Logout
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Content */}
        <main className="flex-1 p-6">
          {renderContent()}
        </main>
      </div>

      {/* E-TRIB Chatbot */}
      <ETRIBChatbot />
    </div>
  );
};

export default Dashboard;
