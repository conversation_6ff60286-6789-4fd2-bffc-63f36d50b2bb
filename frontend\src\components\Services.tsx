import React from 'react';
import { useScrollAnimation, useScrollAnimationMultiple } from '../hooks/useScrollAnimation';

const Services: React.FC = () => {
  const { ref: titleRef, isVisible: titleVisible } = useScrollAnimation();
  const { setRef, visibleItems } = useScrollAnimationMultiple({ threshold: 0.1 });

  const services = [
    {
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>
      ),
      title: "Premium Trading Signals",
      description: "Get high-accuracy forex signals with detailed entry, stop-loss, and take-profit levels delivered in real-time.",
      features: ["95% accuracy rate", "Real-time notifications", "Risk management included", "Multiple currency pairs"]
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 14l9-5-9-5-9 5 9 5z"></path>
          <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
        </svg>
      ),
      title: "Comprehensive Courses",
      description: "Master forex trading with our structured courses covering everything from basics to advanced strategies.",
      features: ["Beginner to advanced levels", "Video tutorials", "Interactive quizzes", "Lifetime access"]
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
          <circle cx="9" cy="7" r="4"></circle>
          <path d="m22 21-3-3m0 0a5.5 5.5 0 1 0-7.78-7.78 5.5 5.5 0 0 0 7.78 7.78Z"></path>
        </svg>
      ),
      title: "One-on-One Coaching",
      description: "Get personalized mentorship from experienced traders to accelerate your learning and improve your results.",
      features: ["Personal mentor", "Customized strategy", "Weekly sessions", "Progress tracking"]
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M3 13h8V3H9v6H3v4zm0 8h6v-2H3v2zm10 0h8v-6h-8v6zm5-12V3h-5v6h5z"/>
        </svg>
      ),
      title: "Live Trading Sessions",
      description: "Join our expert traders in live sessions to see real-time analysis and trading decisions in action.",
      features: ["Daily live sessions", "Real-time analysis", "Q&A opportunities", "Market insights"]
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      ),
      title: "Risk Management Tools",
      description: "Learn and implement proper risk management techniques to protect your capital and maximize profits.",
      features: ["Position sizing calculator", "Risk/reward analysis", "Portfolio management", "Stop-loss strategies"]
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M20 6L9 17l-5-5"/>
        </svg>
      ),
      title: "Community Support",
      description: "Join our active community of traders for discussions, support, and shared learning experiences.",
      features: ["Private Discord server", "24/7 support", "Peer learning", "Success stories"]
    }
  ];

  return (
    <section id="services" className="py-20 bg-gray-50 dark:bg-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div
          ref={titleRef}
          className={`text-center mb-16 transition-all duration-1000 ${
            titleVisible
              ? 'opacity-100 translate-y-0'
              : 'opacity-0 translate-y-10'
          }`}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Our Services
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Comprehensive forex education and trading support designed to help you succeed in the markets.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <div
              key={index}
              ref={setRef(index)}
              className={`card p-6 hover:shadow-xl transition-all duration-700 ${
                visibleItems.has(index)
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-10'
              }`}
              style={{ transitionDelay: `${index * 100}ms` }}
            >
              <div className="w-16 h-16 bg-primary-600 rounded-xl flex items-center justify-center text-white mb-6">
                {service.icon}
              </div>
              
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                {service.title}
              </h3>
              
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                {service.description}
              </p>
              
              <ul className="space-y-2">
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                    <svg className="w-4 h-4 text-primary-600 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <button className="btn-primary text-lg px-8 py-3">
            Get Started Today
          </button>
        </div>
      </div>
    </section>
  );
};

export default Services;
